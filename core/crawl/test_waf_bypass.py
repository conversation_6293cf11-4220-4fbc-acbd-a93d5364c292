#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云WAF绕过测试脚本
测试新的WAF检测和绕过功能
"""

import execjs
import time
from pathlib import Path

def test_waf_bypass():
    """测试WAF绕过功能"""
    print("🔍 Testing Aliyun WAF Bypass Functionality")
    print("=" * 50)
    
    # 模拟WAF挑战响应
    sample_waf_response = '''
    <textarea id="renderData" style="display:none">{"_waf_bd8ce2ce37":"R/9D5uA+j2tqMBcMfR+0XxMcwdKO8hNNP9PNXARJO94="}</textarea>
    <!doctype html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="aliyun_waf_aa" content="371ac51ab53fc53eb6bf9aa462a72e9f">
        <meta name="aliyun_waf_oo" content="var _0x2576duCiQ','MOvsy">
        <meta name="aliyun_waf_00" content="2e2634477379b3758fbde78eaedcba3e">
        <script>
        function getRenderData(){
            var dataTag = document.getElementById('renderData');
            var renderData = dataTag.innerHTML;
            return JSON.parse(renderData);
        }
        </script>
    </head>
    <body>
        <div>WAF Challenge Page</div>
    </body>
    </html>
    '''
    
    try:
        # 加载JavaScript文件
        current_dir = Path(__file__).parent
        
        # 加载WAF绕过脚本
        waf_js_path = current_dir / 'aliyun_waf_bypass.js'
        if not waf_js_path.exists():
            print("❌ aliyun_waf_bypass.js not found")
            return False
        
        with open(waf_js_path, 'r', encoding='utf-8') as f:
            waf_js = f.read()
        
        # 编译JavaScript
        ctx = execjs.compile(waf_js)
        print("✅ JavaScript context loaded successfully")
        
        # 测试WAF绕过
        print("\n🧪 Testing WAF bypass with sample response...")
        result = ctx.call('bypassAliyunWaf', sample_waf_response)
        
        if result.get('success'):
            print("✅ WAF bypass successful!")
            print(f"   Extracted parameters: {list(result.get('params', {}).keys())}")
            print(f"   Response generated: {bool(result.get('response'))}")
            
            # 显示响应详情
            response = result.get('response', {})
            if response:
                print(f"   Timestamp: {response.get('timestamp')}")
                print(f"   Fingerprint: {response.get('fingerprint', 'N/A')}")
                print(f"   WAF Challenge: {response.get('waf_challenge', 'N/A')[:20]}...")
                print(f"   WAF Response: {response.get('waf_response', 'N/A')[:20]}...")
        else:
            print("❌ WAF bypass failed")
            print(f"   Error: {result.get('error', 'Unknown error')}")
            
            # 检查是否有fallback
            fallback = result.get('fallback')
            if fallback:
                print(f"   Fallback available: {bool(fallback)}")
        
        # 测试参数解析
        print("\n🔍 Testing parameter parsing...")
        params = ctx.call('parseWafParams', sample_waf_response)
        print(f"   Parsed parameters: {list(params.keys())}")
        
        for key, value in params.items():
            print(f"   {key}: {value[:30]}{'...' if len(str(value)) > 30 else ''}")
        
        # 测试getRenderData函数
        print("\n📄 Testing getRenderData function...")
        try:
            render_data = ctx.call('getRenderData')
            if render_data:
                print(f"   Render data extracted: {render_data}")
            else:
                print("   No render data found")
        except Exception as e:
            print(f"   getRenderData failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_waf_detection():
    """测试WAF检测功能"""
    print("\n🔍 Testing WAF Detection")
    print("=" * 30)
    
    # 模拟不同类型的响应
    test_cases = [
        {
            'name': 'Normal Response',
            'content': '<html><body><h1>Normal Page</h1></body></html>',
            'expected': False
        },
        {
            'name': 'Aliyun WAF Challenge',
            'content': '<meta name="aliyun_waf_aa" content="test">',
            'expected': True
        },
        {
            'name': 'WAF BD Parameter',
            'content': '{"_waf_bd8ce2ce37":"test"}',
            'expected': True
        },
        {
            'name': 'Render Data',
            'content': '<textarea id="renderData">test</textarea>',
            'expected': True
        }
    ]
    
    # 模拟检测逻辑
    def detect_waf(content):
        content_lower = content.lower()
        indicators = [
            'aliyun_waf_aa', 'aliyun_waf_oo', 'aliyun_waf_00',
            '_waf_bd8ce2ce37', 'renderdata', 'getrenderdata'
        ]
        return any(indicator in content_lower for indicator in indicators)
    
    passed = 0
    total = len(test_cases)
    
    for case in test_cases:
        result = detect_waf(case['content'])
        status = "✅" if result == case['expected'] else "❌"
        print(f"{status} {case['name']}: {'Detected' if result else 'Not detected'}")
        if result == case['expected']:
            passed += 1
    
    print(f"\n📊 Detection accuracy: {passed}/{total} ({passed/total*100:.1f}%)")
    return passed == total

def main():
    """主函数"""
    print("🚀 Starting Aliyun WAF Bypass Tests")
    print("=" * 50)
    
    # 运行测试
    bypass_success = test_waf_bypass()
    detection_success = test_waf_detection()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    print(f"✅ WAF Bypass Test: {'PASS' if bypass_success else 'FAIL'}")
    print(f"✅ WAF Detection Test: {'PASS' if detection_success else 'FAIL'}")
    
    overall_success = bypass_success and detection_success
    
    if overall_success:
        print("\n🎉 All WAF tests passed successfully!")
        print("💡 The enhanced anti-crawling solution should now handle Aliyun WAF challenges.")
        return True
    else:
        print("\n⚠️  Some WAF tests failed. Please check the implementation.")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
