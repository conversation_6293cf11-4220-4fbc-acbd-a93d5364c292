#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
雪球时间线爬虫增强反爬虫解决方案测试脚本
测试改造后的xueqiu-timeline爬虫的反爬虫功能
"""

import sys
import os
import time
import json
import hashlib
import random
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import execjs
    from crawl.spiders.xueqiu_timeline import XueqiuTimelineSpider
    from scrapy.utils.project import get_project_settings
    from scrapy.crawler import CrawlerProcess
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure you're running this from the correct directory and all dependencies are installed")
    sys.exit(1)

class XueqiuTimelineEnhancedTester:
    """雪球时间线增强反爬虫测试器"""
    
    def __init__(self):
        self.spider_class = XueqiuTimelineSpider
        self.test_results = []
        
    def test_spider_initialization(self):
        """测试爬虫初始化"""
        print("\n🔧 Testing Spider Initialization")
        
        try:
            spider = self.spider_class()
            
            # 检查基本属性
            assert hasattr(spider, 'anti_crawl_state'), "Missing anti_crawl_state"
            assert hasattr(spider, 'acw_sc_v2'), "Missing acw_sc_v2"
            assert hasattr(spider, 'session_initialized'), "Missing session_initialized"
            assert hasattr(spider, 'js_context'), "Missing js_context"
            
            # 检查反爬虫状态
            state = spider.anti_crawl_state
            required_keys = ['challenge_count', 'last_challenge_time', 'session_token', 
                           'device_fingerprint', 'request_sequence']
            for key in required_keys:
                assert key in state, f"Missing key in anti_crawl_state: {key}"
            
            # 检查设备指纹
            fingerprint = state['device_fingerprint']
            assert len(fingerprint) == 32, f"Invalid fingerprint length: {len(fingerprint)}"
            assert fingerprint.isalnum(), "Fingerprint should be alphanumeric"
            
            print("  ✓ Spider initialization successful")
            print(f"  ✓ Device fingerprint: {fingerprint[:16]}...")
            print(f"  ✓ Anti-crawl state initialized")
            
            return True
            
        except Exception as e:
            print(f"  ✗ Spider initialization failed: {e}")
            return False
    
    def test_js_context_loading(self):
        """测试JavaScript上下文加载"""
        print("\n🔧 Testing JavaScript Context Loading")
        
        try:
            spider = self.spider_class()
            
            if spider.js_context is None:
                print("  ⚠️  JavaScript context not loaded (files may be missing)")
                return True  # 这不是致命错误
            
            # 测试MD5生成
            test_url = "https://xueqiu.com/v4/statuses/user_timeline.json?page=1&user_id=test"
            try:
                md5_result = spider.js_context.call('getMd5', test_url)
                print(f"  ✓ MD5 generation successful: {md5_result[:20]}...")
            except Exception as e:
                print(f"  ⚠️  MD5 generation failed: {e}")
            
            # 测试acw_sc_v2生成
            test_param = 'FA6AEB89B2318F527AD3AE807660BD7BCE67DDFA'
            try:
                acw_result = spider.js_context.call('get_acw_sc_v2', test_param)
                if acw_result:
                    print(f"  ✓ acw_sc_v2 generation successful: {acw_result[:20]}...")
                else:
                    print("  ⚠️  acw_sc_v2 generation returned empty result")
            except Exception as e:
                print(f"  ⚠️  acw_sc_v2 generation failed: {e}")
            
            return True
            
        except Exception as e:
            print(f"  ✗ JavaScript context test failed: {e}")
            return False
    
    def test_url_enhancement(self):
        """测试URL增强功能"""
        print("\n🔧 Testing URL Enhancement")
        
        try:
            spider = self.spider_class()
            
            # 测试基本URL
            base_url = "https://xueqiu.com/v4/statuses/user_timeline.json?page=1&user_id=123456"
            
            # 测试URL增强
            enhanced_url = spider._enhance_url_with_params(base_url)
            
            # 检查增强参数
            enhanced_params = ['seq', 'fp', 'ts', 'r']
            for param in enhanced_params:
                assert param in enhanced_url, f"Missing enhanced parameter: {param}"
            
            # 如果有JS上下文，检查MD5参数
            if spider.js_context:
                assert 'md5__1038' in enhanced_url, "Missing MD5 parameter"
            
            print(f"  ✓ URL enhancement successful")
            print(f"  ✓ Original: {base_url[:60]}...")
            print(f"  ✓ Enhanced: {enhanced_url[:80]}...")
            
            return True
            
        except Exception as e:
            print(f"  ✗ URL enhancement test failed: {e}")
            return False
    
    def test_headers_preparation(self):
        """测试请求头准备"""
        print("\n🔧 Testing Headers Preparation")
        
        try:
            spider = self.spider_class()
            
            # 测试增强请求头
            headers = spider._prepare_enhanced_headers()
            
            # 检查必要的请求头
            required_headers = [
                'Accept', 'Accept-Language', 'Accept-Encoding',
                'User-Agent', 'X-Requested-With', 'X-Device-Fingerprint',
                'X-Request-Sequence'
            ]
            
            for header in required_headers:
                assert header in headers, f"Missing required header: {header}"
            
            # 检查User-Agent
            user_agent = headers['User-Agent']
            assert 'Mozilla' in user_agent, "Invalid User-Agent format"
            assert 'Chrome' in user_agent or 'Safari' in user_agent, "User-Agent should contain browser info"
            
            print(f"  ✓ Headers preparation successful")
            print(f"  ✓ User-Agent: {user_agent[:50]}...")
            print(f"  ✓ Device fingerprint: {headers['X-Device-Fingerprint'][:16]}...")
            
            return True
            
        except Exception as e:
            print(f"  ✗ Headers preparation test failed: {e}")
            return False
    
    def test_captcha_detection(self):
        """测试验证码检测功能"""
        print("\n🔧 Testing Captcha Detection")
        
        try:
            spider = self.spider_class()
            
            # 模拟响应对象
            class MockResponse:
                def __init__(self, text, status=200):
                    self.text = text
                    self.status = status
                    self.headers = {}
            
            # 测试正常响应
            normal_response = MockResponse('{"statuses": []}')
            assert not spider._is_captcha_response(normal_response), "Normal response detected as captcha"
            
            # 测试验证码响应
            captcha_response = MockResponse('<html><body>验证码</body></html>')
            assert spider._is_captcha_response(captcha_response), "Captcha response not detected"
            
            # 测试阿里云WAF响应
            waf_response = MockResponse('<meta name="aliyun_waf_aa" content="test">')
            assert spider._is_captcha_response(waf_response), "WAF response not detected"
            
            # 测试403响应
            forbidden_response = MockResponse('Forbidden', status=403)
            assert spider._is_captcha_response(forbidden_response), "403 response not detected"
            
            print("  ✓ Captcha detection working correctly")
            print("  ✓ Normal response: Not detected as captcha")
            print("  ✓ Captcha response: Correctly detected")
            print("  ✓ WAF response: Correctly detected")
            print("  ✓ 403 response: Correctly detected")
            
            return True
            
        except Exception as e:
            print(f"  ✗ Captcha detection test failed: {e}")
            return False
    
    def test_spider_config(self):
        """测试爬虫配置"""
        print("\n🔧 Testing Spider Configuration")
        
        try:
            spider = self.spider_class()
            config = spider.get_spider_config()
            
            # 检查配置参数
            assert config.concurrent_requests == 1, "Concurrent requests should be 1 for anti-crawling"
            assert config.download_delay >= 10, "Download delay should be >= 10 seconds"
            assert config.batch_size <= 5, "Batch size should be <= 5 for conservative crawling"
            assert config.retry_times >= 5, "Retry times should be >= 5"
            
            print(f"  ✓ Spider configuration is conservative")
            print(f"  ✓ Concurrent requests: {config.concurrent_requests}")
            print(f"  ✓ Download delay: {config.download_delay}s")
            print(f"  ✓ Batch size: {config.batch_size}")
            print(f"  ✓ Retry times: {config.retry_times}")
            
            return True
            
        except Exception as e:
            print(f"  ✗ Spider configuration test failed: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting Xueqiu Timeline Enhanced Anti-Crawling Tests")
        print("=" * 60)
        
        tests = [
            self.test_spider_initialization,
            self.test_js_context_loading,
            self.test_url_enhancement,
            self.test_headers_preparation,
            self.test_captcha_detection,
            self.test_spider_config
        ]
        
        passed_tests = 0
        for test in tests:
            if test():
                passed_tests += 1
        
        print("\n" + "=" * 60)
        print(f"📊 Test Results: {passed_tests}/{len(tests)} tests passed")
        
        if passed_tests == len(tests):
            print("🎉 All tests passed! The enhanced spider is ready for use.")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")
        
        return passed_tests == len(tests)

def main():
    """主函数"""
    print("Xueqiu Timeline Enhanced Anti-Crawling Solution Tester")
    print("Testing the enhanced xueqiu-timeline spider...")
    
    tester = XueqiuTimelineEnhancedTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Enhanced spider is ready for production use!")
        print("\n📝 Usage:")
        print("   scrapy crawl xueqiu-timeline")
        print("\n🔧 Configuration:")
        print("   - Make sure user IDs are configured in the database")
        print("   - Ensure JavaScript files (test.js, acw_sc_v2.js, aliyun_waf_bypass.js) are present")
        print("   - Monitor logs for anti-crawling challenges")
    else:
        print("\n❌ Please fix the issues before using the spider.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
