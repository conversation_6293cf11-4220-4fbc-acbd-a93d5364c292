# -*- coding: utf-8 -*-

"""
雪球用户时间线爬虫 - 增强反爬虫解决方案
爬取特定雪球用户的动态
数据源：xueqiu.com

基于阿里云文档的反爬虫解决方案：
1. 处理阿里系加密参数 acw_sc__v2
2. 绕过反调试机制
3. 模拟真实浏览器行为
4. 动态生成请求参数
5. 阿里云WAF绕过
"""

import time
import random
import execjs
import json
import hashlib
import base64
from urllib.parse import urlparse, parse_qs

from crawl.items import MediaItem
from crawl.tools.spider_base import NewsSpider, SpiderConfig
from crawl.tools.performance import timing_decorator
from crawl.tools.data_processor import create_news_processor
from crawl.tools.helper import clear_html, ago_day_timestr, fetch_quant_raw, filter_emoji, get_xueqiu_cookie, getFirstSentence, today_timestamp
from scrapy.http import FormRequest, Request


class XueqiuTimelineSpider(NewsSpider):
    """
    雪球用户时间线爬虫 - 增强反爬虫版本
    爬取特定雪球用户的动态
    """
    name = "xueqiu-timeline"
    allowed_domains = ["xueqiu.com"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_processor = create_news_processor()
        self.cookies = get_xueqiu_cookie()
        self.times = 0
        self.max_pages = 10
        self.acw_sc_v2 = None
        self.session_initialized = False

        # 增强的反爬虫状态管理
        self.anti_crawl_state = {
            'challenge_count': 0,
            'last_challenge_time': 0,
            'session_token': None,
            'device_fingerprint': self._generate_device_fingerprint(),
            'request_sequence': 0
        }

        try:
            self.quant = fetch_quant_raw()
            self.users = self.quant.xueqiuUser.split(',') if hasattr(self.quant, 'xueqiuUser') else []
        except Exception as e:
            self.logger.warning(f"Failed to fetch quant data: {e}")
            self.quant = None
            self.users = []

        # Debug: Log cookie information
        self.logger.info(f"Loaded {len(self.cookies)} cookies for Xueqiu Timeline")
        for name, value in self.cookies.items():
            self.logger.debug(f"Cookie: {name} = {value[:20]}{'...' if len(value) > 20 else ''}")

        # 更真实的浏览器headers，模拟最新Chrome
        self.default_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Ch-Ua': '"Google Chrome";v="120", "Chromium";v="120", "Not_A Brand";v="99"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://xueqiu.com/',
            'Origin': 'https://xueqiu.com'
        }

        # 增强的JS上下文初始化
        self.js_context = self._init_enhanced_js_context()


    def _generate_device_fingerprint(self):
        """生成设备指纹，模拟真实浏览器环境"""
        import platform
        import uuid

        # 基于系统信息生成稳定的设备指纹
        system_info = f"{platform.system()}-{platform.machine()}-{platform.processor()}"
        mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                               for elements in range(0,2*6,2)][::-1])

        fingerprint_data = {
            'screen': {'width': 1920, 'height': 1080, 'colorDepth': 24},
            'timezone': -480,  # UTC+8
            'language': 'zh-CN',
            'platform': 'MacIntel',
            'userAgent': self._get_random_user_agent(),
            'webgl': 'ANGLE (Apple, Apple M1 Pro, OpenGL 4.1)',
            'canvas': hashlib.md5(system_info.encode()).hexdigest()[:16],
            'audio': hashlib.md5(mac_address.encode()).hexdigest()[:16]
        }

        return hashlib.md5(json.dumps(fingerprint_data, sort_keys=True).encode()).hexdigest()

    def _init_enhanced_js_context(self):
        """增强的JavaScript执行环境初始化"""
        try:
            # 加载test.js - 包含MD5验证逻辑
            try:
                with open('./test.js', 'r', encoding='utf-8') as f:
                    md5_js = f.read()
            except FileNotFoundError:
                md5_js = "function getMd5(url) { return 'test_md5_' + Date.now(); }"

            # 加载acw_sc_v2.js - 包含阿里系加密参数逻辑
            try:
                with open('./acw_sc_v2.js', 'r', encoding='utf-8') as f:
                    acw_sc_v2_js = f.read()
            except FileNotFoundError:
                acw_sc_v2_js = "function get_acw_sc_v2(arg1) { return 'fallback_' + Date.now(); }"

            # 加载阿里云WAF绕过逻辑
            try:
                with open('./aliyun_waf_bypass.js', 'r', encoding='utf-8') as f:
                    waf_bypass_js = f.read()
            except FileNotFoundError:
                waf_bypass_js = """
                function bypassAliyunWaf(htmlContent) {
                    return {
                        success: false,
                        error: 'WAF bypass not available',
                        fallback: { timestamp: Date.now() }
                    };
                }
                """

            # 添加增强的反调试绕过代码
            anti_debug_js = """
            // 反调试绕过 - Hook debugger语句
            var originalDebugger = Function.prototype.constructor;
            Function.prototype.constructor = function() {
                var args = Array.prototype.slice.call(arguments);
                if (args.length > 0 && args[args.length - 1].indexOf('debugger') !== -1) {
                    args[args.length - 1] = args[args.length - 1].replace(/debugger/g, '');
                }
                return originalDebugger.apply(this, args);
            };

            // Hook console方法
            if (typeof console !== 'undefined') {
                console.clear = function() {};
                console.log = function() {};
                console.warn = function() {};
                console.error = function() {};
            }
            """

            # 合并所有JS代码
            all_js = anti_debug_js + '\n' + md5_js + '\n' + acw_sc_v2_js + '\n' + waf_bypass_js
            ctx = execjs.compile(all_js)
            self.logger.info("Successfully initialized enhanced JS context with WAF bypass support")
            return ctx
        except Exception as e:
            self.logger.warning(f"Failed to load enhanced JS context: {e}")
            return None

    def get_spider_config(self) -> SpiderConfig:
        """获取爬虫配置 - 雪球时间线需要保守配置避免被限制"""
        return SpiderConfig(
            concurrent_requests=1,
            download_delay=15,  # 增加延迟避免被检测
            batch_size=2,       # 减少批次大小
            retry_times=8       # 增加重试次数
        )

    def get_start_urls(self) -> list:
        """获取起始URL列表 - 增强参数生成"""
        return []  # 使用start方法

    def _get_random_user_agent(self):
        """生成随机的User-Agent - 更新到最新版本"""
        user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15'
        ]
        return random.choice(user_agents)

    def _add_random_delay(self, min_delay=8, max_delay=20):
        """添加随机延迟模拟人类行为 - 增强版本"""
        # 基于反爬虫挑战次数动态调整延迟
        base_delay = random.uniform(min_delay, max_delay)
        challenge_multiplier = 1 + (self.anti_crawl_state['challenge_count'] * 0.5)
        final_delay = min(base_delay * challenge_multiplier, 60)  # 最大60秒

        self.logger.debug(f"Adding random delay: {final_delay:.2f} seconds (challenge_count: {self.anti_crawl_state['challenge_count']})")
        time.sleep(final_delay)

    def _get_session_request(self):
        """获取会话初始化请求 - 多步骤会话建立"""
        # 第一步：访问主页
        return Request(
            url='https://xueqiu.com/',
            headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Sec-Ch-Ua': '"Google Chrome";v="120", "Chromium";v="120", "Not_A Brand";v="99"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"macOS"',
                'User-Agent': self._get_random_user_agent(),
                'DNT': '1',  # Do Not Track
                'Connection': 'keep-alive'
            },
            meta={
                'cookiejar': 1,
                'dont_cache': True,
                'download_timeout': 45,
                'session_step': 'homepage'
            },
            cookies=self.cookies,
            callback=self._handle_session_response,
            dont_filter=True
        )

    def _handle_session_response(self, response):
        """处理会话初始化响应 - 增强版本"""
        session_step = response.meta.get('session_step', 'homepage')
        self.logger.info(f"Session step '{session_step}' completed with status: {response.status}")

        # 检查是否遇到验证码或反爬虫挑战
        if self._is_captcha_response(response):
            self.anti_crawl_state['challenge_count'] += 1
            self.anti_crawl_state['last_challenge_time'] = time.time()
            self.logger.error(f"Anti-crawling challenge detected (count: {self.anti_crawl_state['challenge_count']})")

            # 检查是否是阿里云WAF挑战
            if hasattr(response, 'text') and any(indicator in response.text.lower() for indicator in ['aliyun_waf', '_waf_bd8ce2ce37', 'renderdata']):
                self.logger.info("Detected Aliyun WAF challenge, attempting to handle...")
                waf_request = self._handle_aliyun_waf_challenge(response)
                if waf_request:
                    yield waf_request
                    return

            # 如果挑战次数过多，使用更激进的策略
            if self.anti_crawl_state['challenge_count'] >= 3:
                self.logger.error("Too many challenges, switching to stealth mode")
                yield self._get_stealth_session_request()
                return
            else:
                # 尝试使用代理IP重新连接
                yield self._get_proxy_session_request()
                return

        # 多步骤会话建立
        if session_step == 'homepage':
            # 第二步：访问今日页面获取更多cookie
            yield self._get_today_page_request()
            return
        elif session_step == 'today':
            # 第三步：处理acw_sc_v2参数
            self._extract_and_process_acw_sc_v2(response)

        # 从响应Cookie中提取acw_sc_v2
        self._extract_acw_sc_v2(response)

        # 增强的acw_sc_v2生成逻辑
        if not self.acw_sc_v2 and self.js_context:
            try:
                # 使用多种参数尝试生成
                challenge_params = [
                    'FA6AEB89B2318F527AD3AE807660BD7BCE67DDFA',
                    self.anti_crawl_state['device_fingerprint'][:40],
                    hashlib.md5(str(time.time()).encode()).hexdigest().upper()
                ]

                for param in challenge_params:
                    try:
                        self.acw_sc_v2 = self.js_context.call('get_acw_sc_v2', param)
                        if self.acw_sc_v2:
                            self.logger.info(f"Generated acw_sc_v2 using param {param[:10]}...: {self.acw_sc_v2[:10]}...")
                            break
                    except Exception as e:
                        self.logger.debug(f"Failed with param {param[:10]}...: {e}")
                        continue

            except Exception as e:
                self.logger.warning(f"Failed to generate acw_sc_v2: {e}")

        # 更新cookies
        if self.acw_sc_v2:
            self.cookies['acw_sc__v2'] = self.acw_sc_v2

        self.session_initialized = True
        self.logger.info(f"Session fully initialized, acw_sc_v2: {self.acw_sc_v2[:10] if self.acw_sc_v2 else 'None'}...")

        # 添加随机延迟模拟人类行为
        self._add_random_delay(10, 25)

        # 会话建立后，开始实际的数据请求
        yield from self._generate_timeline_requests()

    async def start(self):
        """异步生成初始请求 (替代已弃用的start_requests())"""
        # 首先建立会话
        yield self._get_session_request()

    def start_requests(self):
        """生成用户时间线请求 (已弃用，请使用start()方法)"""
        return super().start_requests()

    def _generate_timeline_requests(self):
        """生成用户时间线请求"""
        if not self.js_context:
            self.logger.error("JS context not available, cannot generate MD5")
            return

        if not self.users:
            self.logger.warning("No users configured for timeline crawling")
            return

        for user in self.users:
            if user.strip():
                for page in range(1, self.max_pages):
                    try:
                        # 构建增强的URL
                        timestamp = str(today_timestamp(ms=False))
                        base_url = f'https://xueqiu.com/v4/statuses/user_timeline.json'
                        params = {
                            'page': page,
                            'user_id': user,
                            'type': 0,
                            '_': timestamp,
                            'x': random.randint(1000, 9999),  # 随机参数
                            'v': '6.0.0'  # 版本参数
                        }

                        # 构建完整URL
                        param_str = '&'.join([f'{k}={v}' for k, v in params.items()])
                        full_url = f'{base_url}?{param_str}'

                        # 增强URL参数
                        enhanced_url = self._enhance_url_with_params(full_url)

                        yield Request(
                            url=enhanced_url,
                            headers=self._prepare_enhanced_headers(),
                            meta={
                                'cookiejar': 1,
                                'dont_cache': True,
                                'download_timeout': 45,
                                'download_delay': random.uniform(8, 15),
                                'user_id': user,
                                'page': page
                            },
                            cookies=self.cookies,
                            callback=self.parse_response,
                            dont_filter=True
                        )

                        # 添加页面间延迟
                        self._add_random_delay(5, 12)

                    except Exception as e:
                        self.logger.error(f"Error generating request for user {user}, page {page}: {e}")

    def _enhance_url_with_params(self, url):
        """增强URL参数 - 添加更多真实参数"""
        self.anti_crawl_state['request_sequence'] += 1

        # 解析现有URL
        parsed = urlparse(url)
        params = parse_qs(parsed.query)

        # 添加增强参数
        enhanced_params = {
            'seq': str(self.anti_crawl_state['request_sequence']),
            'fp': self.anti_crawl_state['device_fingerprint'][:16],
            'ts': str(int(time.time() * 1000)),
            'r': str(random.random())[:8]
        }

        # 合并参数
        for key, value in enhanced_params.items():
            params[key] = [value]

        # 如果有JS上下文，添加MD5验证
        if self.js_context:
            try:
                # 构建临时URL用于MD5计算
                temp_params = '&'.join([f'{k}={v[0]}' for k, v in params.items()])
                temp_url = f'{parsed.scheme}://{parsed.netloc}{parsed.path}?{temp_params}'
                md5 = self.js_context.call('getMd5', temp_url)
                params['md5__1038'] = [md5]
            except Exception as e:
                self.logger.warning(f"Failed to generate MD5: {e}")

        # 重建URL
        final_params = '&'.join([f'{k}={v[0]}' for k, v in params.items()])
        return f'{parsed.scheme}://{parsed.netloc}{parsed.path}?{final_params}'

    def _prepare_enhanced_headers(self):
        """准备增强的请求头"""
        headers = self.default_headers.copy()
        headers['User-Agent'] = self._get_random_user_agent()

        # 添加更多真实的浏览器头
        headers.update({
            'X-Requested-With': 'XMLHttpRequest',
            'X-Device-Fingerprint': self.anti_crawl_state['device_fingerprint'][:32],
            'X-Request-Sequence': str(self.anti_crawl_state['request_sequence']),
            'Connection': 'keep-alive'
        })

        return headers

    @timing_decorator('xueqiu_timeline.parse_response')
    def parse_response(self, response):
        """解析用户时间线响应 - 增强版本"""
        try:
            # 检查是否遇到验证码
            if self._is_captcha_response(response):
                self.logger.error(f"Captcha detected for {response.url}. Spider may be blocked.")
                self.logger.error(f"Response status: {response.status}")
                self.logger.error(f"Response headers: {dict(response.headers)}")
                # 尝试使用代理重新请求
                if hasattr(response, 'request') and response.request.url != 'https://xueqiu.com/':
                    yield self._create_retry_with_proxy_request(response.request.url)
                # 停止爬取避免进一步被检测
                return

            json_data = self.parse_json_response(response)
            if not json_data:
                self.logger.warning(f"No JSON data from {response.url}")
                self.logger.warning(f"Response status: {response.status}")
                self.logger.warning(f"Response body preview: {response.text[:500]}")
                # 尝试刷新会话
                if not self.session_initialized or random.random() < 0.1:
                    yield self._get_session_request()
                return

            if 'statuses' not in json_data:
                self.logger.warning(f"No 'statuses' key in response from {response.url}")
                self.logger.warning(f"Response keys: {list(json_data.keys())}")
                if 'error_description' in json_data:
                    self.logger.error(f"API Error: {json_data['error_description']} (Code: {json_data.get('error_code', 'Unknown')})")
                    # 错误时刷新会话
                    yield self._get_session_request()
                return

            statuses = json_data.get('statuses', [])
            if not statuses:
                self.logger.info(f"No statuses found for user {response.meta.get('user_id', 'unknown')}")
                return

            # 时间过滤器
            time_filter = self._get_time_filter()

            # 处理状态列表
            for status in statuses:
                if self._is_valid_status(status, time_filter):
                    item = self._create_status_item(status)
                    if item:
                        yield item

        except Exception as e:
            self.handle_error(e, response)
            # 发生异常时，尝试刷新会话
            yield self._get_session_request()

    def _get_today_page_request(self):
        """获取今日页面请求 - 第二步会话建立"""
        return Request(
            url='https://xueqiu.com/today',
            headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Ch-Ua': '"Google Chrome";v="120", "Chromium";v="120", "Not_A Brand";v="99"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"macOS"',
                'User-Agent': self._get_random_user_agent(),
                'Referer': 'https://xueqiu.com/',
                'DNT': '1'
            },
            meta={
                'cookiejar': 1,
                'dont_cache': True,
                'download_timeout': 45,
                'session_step': 'today'
            },
            cookies=self.cookies,
            callback=self._handle_session_response,
            dont_filter=True
        )

    def _get_stealth_session_request(self):
        """获取隐秘模式会话请求 - 应对高强度反爬虫"""
        # 更换User-Agent和其他指纹信息
        self.anti_crawl_state['device_fingerprint'] = self._generate_device_fingerprint()

        return Request(
            url='https://xueqiu.com/',
            headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',  # 切换到英文
                'Accept-Encoding': 'gzip, deflate, br',
                'Cache-Control': 'max-age=0',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',  # 稍旧版本
                'DNT': '1'
            },
            meta={
                'cookiejar': 2,  # 使用新的cookie jar
                'dont_cache': True,
                'download_timeout': 60,
                'session_step': 'stealth'
            },
            callback=self._handle_session_response,
            dont_filter=True
        )

    def _get_proxy_session_request(self):
        """使用代理IP重新建立会话"""
        # 这里可以接入代理池，返回使用代理的请求
        # 简化版本：只是重新请求，但实际项目中应该实现代理轮换
        self.logger.info("Attempting to use proxy for session initialization")
        request = self._get_session_request()
        # 在实际项目中，这里应该从代理池获取代理并设置
        # request.meta['proxy'] = 'http://proxy_ip:proxy_port'
        return request

    def _extract_and_process_acw_sc_v2(self, response):
        """提取并处理acw_sc_v2参数 - 增强版本"""
        # 检查响应体中的JavaScript代码
        if hasattr(response, 'text'):
            # 查找可能的acw_sc_v2生成代码
            import re

            # 查找VM文件中的reload函数
            vm_pattern = r'reload\s*\(\s*["\']([^"\']+)["\']\s*\)'
            vm_matches = re.findall(vm_pattern, response.text)

            if vm_matches:
                self.logger.info(f"Found VM reload parameters: {vm_matches}")
                # 尝试使用找到的参数生成acw_sc_v2
                for param in vm_matches:
                    if self.js_context:
                        try:
                            self.acw_sc_v2 = self.js_context.call('get_acw_sc_v2', param)
                            if self.acw_sc_v2:
                                self.logger.info(f"Generated acw_sc_v2 from VM param: {self.acw_sc_v2[:10]}...")
                                break
                        except Exception as e:
                            self.logger.debug(f"Failed with VM param {param}: {e}")

    def _extract_acw_sc_v2(self, response):
        """从响应中提取acw_sc_v2参数"""
        # 从响应头的Set-Cookie中提取
        set_cookie = response.headers.getlist('Set-Cookie')
        for cookie in set_cookie:
            cookie_str = cookie.decode('utf-8') if isinstance(cookie, bytes) else cookie
            if 'acw_sc__v2=' in cookie_str:
                try:
                    acw_sc_v2_value = cookie_str.split('acw_sc__v2=')[1].split(';')[0]
                    self.acw_sc_v2 = acw_sc_v2_value
                    self.logger.info(f"Extracted acw_sc_v2 from Set-Cookie: {self.acw_sc_v2[:10]}...")
                    return
                except Exception as e:
                    self.logger.warning(f"Failed to extract acw_sc_v2: {e}")

        # 检查响应体中是否包含acw_sc_v2
        if hasattr(response, 'text'):
            if 'acw_sc__v2' in response.text:
                self.logger.info("Found acw_sc__v2 reference in response body")

    def _is_captcha_response(self, response) -> bool:
        """检查是否是验证码页面 - 增强版本支持阿里云WAF检测"""
        if response.status == 302:
            location = response.headers.get('Location', b'').decode('utf-8')
            if 'captcha' in location.lower():
                return True

        # 检查阿里云WAF特征
        if hasattr(response, 'text'):
            body_text = response.text.lower()

            # 阿里云WAF特征检测
            aliyun_waf_indicators = [
                'aliyun_waf_aa',
                'aliyun_waf_oo',
                'aliyun_waf_00',
                '_waf_bd8ce2ce37',
                'renderdata',
                'getrenderdata',
                'var _0x2576',
                'function getrenderdata'
            ]

            # 传统验证码检测
            captcha_indicators = ['captcha', '验证码', '人机验证', 'robot', 'verification', '安全验证']

            # 检查所有指标
            all_indicators = aliyun_waf_indicators + captcha_indicators
            if any(indicator in body_text for indicator in all_indicators):
                # 记录具体触发的指标
                triggered = [indicator for indicator in all_indicators if indicator in body_text]
                self.logger.warning(f"Anti-crawling challenge detected. Triggered indicators: {triggered}")
                return True

        # 检查状态码
        if response.status == 403:
            self.logger.warning("403 Forbidden response detected, possibly blocked")
            return True

        # 检查响应长度异常（WAF页面通常很短）
        if hasattr(response, 'text') and len(response.text) < 1000:
            if 'html' in response.text.lower() and 'script' in response.text.lower():
                self.logger.warning("Suspicious short HTML response detected")
                return True

        return False

    def _handle_aliyun_waf_challenge(self, response):
        """处理阿里云WAF挑战 - 使用JavaScript绕过"""
        self.logger.info("Handling Aliyun WAF challenge with JavaScript bypass...")

        try:
            if not self.js_context:
                self.logger.warning("JavaScript context not available, using fallback")
                return self._create_fallback_request()

            # 使用JavaScript处理WAF挑战
            waf_result = self.js_context.call('bypassAliyunWaf', response.text)

            if waf_result.get('success'):
                self.logger.info("WAF bypass successful")
                waf_response = waf_result.get('response', {})

                # 更新cookies with WAF response
                if 'waf_response' in waf_response:
                    self.cookies['aliyun_waf_response'] = waf_response['waf_response']

                # 创建带有WAF响应的请求
                return self._create_waf_verified_request(waf_response)
            else:
                self.logger.warning(f"WAF bypass failed: {waf_result.get('error', 'Unknown error')}")
                # 使用fallback数据
                fallback_data = waf_result.get('fallback', {})
                return self._create_waf_verified_request(fallback_data)

        except Exception as e:
            self.logger.error(f"Failed to handle Aliyun WAF challenge: {e}")
            return self._create_fallback_request()

    def _create_waf_verified_request(self, waf_data):
        """创建经过WAF验证的请求"""
        self.logger.info("Creating WAF-verified request...")

        # 添加WAF相关的headers
        headers = self._prepare_enhanced_headers()
        headers.update({
            'X-Waf-Timestamp': str(waf_data.get('timestamp', int(time.time() * 1000))),
            'X-Waf-Random': waf_data.get('random', str(random.random())[:8]),
            'X-Waf-Fingerprint': waf_data.get('fingerprint', self.anti_crawl_state['device_fingerprint'][:16])
        })

        # 如果有WAF响应，添加到headers
        if 'waf_response' in waf_data:
            headers['X-Aliyun-Waf-Response'] = waf_data['waf_response']

        # 创建请求到时间线API
        return Request(
            url='https://xueqiu.com/',
            headers=headers,
            meta={
                'cookiejar': 1,
                'dont_cache': True,
                'download_timeout': 45,
                'waf_verified': True
            },
            cookies=self.cookies,
            callback=self._handle_session_response,
            dont_filter=True
        )

    def _create_fallback_request(self):
        """创建备用请求策略"""
        self.logger.info("Using fallback strategy for WAF challenge")

        # 增加更长的延迟
        self._add_random_delay(30, 60)

        # 使用更保守的请求策略
        return Request(
            url='https://xueqiu.com/',
            headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0'  # 使用Firefox UA
            },
            meta={
                'cookiejar': 2,  # 使用新的cookie jar
                'dont_cache': True,
                'download_timeout': 60,
                'fallback_mode': True
            },
            callback=self._handle_session_response,
            dont_filter=True
        )

    def _create_retry_with_proxy_request(self, url):
        """创建使用代理的重试请求"""
        self.logger.info(f"Creating retry request with proxy for: {url}")
        # 确保URL包含时间戳
        if '_=' not in url:
            timestamp = str(today_timestamp(ms=False))
            url = f'{url}&_={timestamp}' if '?' in url else f'{url}?_={timestamp}'

        # 添加MD5验证（如果有JS上下文）
        if self.js_context:
            try:
                md5 = self.js_context.call('getMd5', url)
                url = f'{url}&md5__1038={md5}'
            except Exception as e:
                self.logger.warning(f"Failed to generate MD5 for retry: {e}")

        request = Request(
            url=url,
            headers=self._prepare_enhanced_headers(),
            meta={
                'cookiejar': 1,
                'dont_cache': True,
                'download_timeout': 30,
                'retry_times': 1
            },
            cookies=self.cookies,
            callback=self.parse_response,
            dont_filter=True
        )

        # 在实际项目中，这里应该从代理池获取代理并设置
        # request.meta['proxy'] = 'http://proxy_ip:proxy_port'

        return request

    def _get_time_filter(self) -> str:
        """获取时间过滤器"""
        try:
            if self.quant and hasattr(self.quant, 'stockMediaDay'):
                return ago_day_timestr(self.quant.stockMediaDay, '%Y-%m-%d')
            else:
                return ago_day_timestr(7, '%Y-%m-%d')  # 默认7天
        except Exception as e:
            self.logger.warning(f"Failed to get time filter: {e}")
            return ago_day_timestr(7, '%Y-%m-%d')

    def _is_valid_status(self, status_data: dict, time_filter: str) -> bool:
        """验证状态数据是否有效"""
        try:
            if not all(status_data.get(field) for field in ['id', 'description', 'created_at']):
                return False

            # 检查时间过滤
            created_at = status_data.get('created_at', 0)
            if created_at:
                create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))
                return create_time >= time_filter

            return True
        except Exception as e:
            self.logger.warning(f"Error validating status: {e}")
            return False

    def _create_status_item(self, status_data: dict) -> dict:
        """创建状态数据项"""
        try:
            # 处理时间
            created_at = status_data.get('created_at', 0)
            create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))

            # 处理标题（截取描述的第一句话）
            description = status_data.get('description', '').replace('<br/>', '\n')
            title = filter_emoji(getFirstSentence(description))

            # 创建数据项
            item = MediaItem()
            item['mediaId'] = str(status_data.get('id', ''))
            item['source'] = 'xiaozuowen'  # 保持原有的source名称
            item['title'] = title
            item['content'] = filter_emoji(status_data.get('text', ''))
            item['url'] = f"https://xueqiu.com/{status_data.get('user_id', '')}/{status_data.get('id', '')}"
            item['createTime'] = create_time

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating status item: {e}")
            return None


# 添加友好的异常处理，捕获JS执行环境相关的异常
try:
    # 尝试导入execjs，确保它在环境中可用
    import execjs
except ImportError:
    pass