# -*- coding: utf-8 -*-

"""
股票详情爬虫
爬取个股的详细数据，包括收入、业务分析、热门文章等
数据源：xueqiu.com, eastmoney.com
"""

import json
import time
import hashlib
import random
import execjs
import base64
from urllib.parse import urlparse, parse_qs

from crawl.items import MediaItem, Stock2Item, FundItem
from crawl.tools.spider_base import DataSpider, SpiderConfig
from crawl.tools.performance import timing_decorator
from crawl.tools.data_processor import create_stock_processor
from crawl.tools.helper import stock_code, ago_day_timestamp, ago_day_timestr, fetch_quant_raw, get_xueqiu_cookie, clear_html, today_timestamp
from crawl.db.db import DBSession, Stock, Media
from scrapy.http import FormRequest, Request
from sqlalchemy.dialects.mysql import insert
from scrapy_redis.spiders import RedisSpider


class StockDetailSpider(RedisSpider, DataSpider):
    """
    股票详情爬虫 - Redis分布式版本
    爬取个股的详细数据，包括收入、业务分析、热门文章等
    支持多种数据源的分布式爬取
    """
    name = 'stock-detail'
    allowed_domains = ['eastmoney.com', 'xueqiu.com']

    # Redis 分布式配置
    redis_key = 'stock-detail:start_urls'
    
    # Redis 相关配置
    custom_settings = {
        'SCHEDULER': 'scrapy_redis.scheduler.Scheduler',
        'DUPEFILTER_CLASS': 'scrapy_redis.dupefilter.RFPDupeFilter',
        'SCHEDULER_PERSIST': True,
        'REDIS_START_URLS_AS_SET': True,  # 使用 SET 类型
        'REDIS_START_URLS_KEY': 'stock-detail:start_urls',
        
        # 调整并发和延迟设置
        'CONCURRENT_REQUESTS': 3,  # 降低并发数
        'DOWNLOAD_DELAY': 3,  # 增加延迟
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        
        # 重试设置
        'RETRY_ENABLED': True,
        'RETRY_TIMES': 4,  # 增加重试次数
        'RETRY_HTTP_CODES': [500, 502, 503, 504, 400, 401, 403, 404, 408, 429, 504],
        'RETRY_PRIORITY_ADJUST': -1,  # 重试时降低优先级
        
        # 超时设置
        'DOWNLOAD_TIMEOUT': 30,  # 增加超时时间
        
        # 请求头设置
        'DEFAULT_REQUEST_HEADERS': {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36',
            'Connection': 'keep-alive',
            'Accept-Encoding': 'gzip, deflate, br',
        },
        
        # 代理设置
        'DOWNLOADER_MIDDLEWARES': {
            'scrapy.downloadermiddlewares.retry.RetryMiddleware': 90,
            'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware': 110,
            'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
        },
        
        # Cookie设置
        'COOKIES_ENABLED': True,
        'COOKIES_DEBUG': True,
        
        # Log设置
        'LOG_LEVEL': 'DEBUG'
    }

    def next_requests(self):
        """重写next_requests方法，使用SPOP处理SET类型数据"""
        found = 0
        batch_size = getattr(self, 'redis_batch_size', 100)  # 设置一个默认值
        
        while found < batch_size:
            try:
                data = self.server.spop(self.redis_key)
                if not data:
                    self.logger.debug("No more URLs in Redis queue")
                    break
                
                # 解析数据并记录日志
                if isinstance(data, bytes):
                    data = data.decode('utf-8')
                try:
                    url_data = json.loads(data.strip())
                    self.logger.info(f"Processing URL: {url_data.get('url')[:100]}, Type: {url_data.get('data_type')}, Code: {url_data.get('code')}")
                except json.JSONDecodeError:
                    self.logger.error(f"Invalid JSON data in Redis: {data[:200]}")
                    continue

                # 创建请求
                req = self.make_requests_from_data(data)
                if req:
                    yield req
                    found += 1
                    self.logger.debug(f"Successfully created request {found}/{batch_size}")
                else:
                    self.logger.warning("Failed to create request from data")
                    
            except Exception as e:
                self.logger.error(f"Error processing Redis data: {str(e)}")
                continue  # 继续处理下一条数据，而不是中断

    def setup_redis(self, crawler=None):
        """重写setup_redis方法"""
        super().setup_redis(crawler)

    def __init__(self, code=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.code = code
        self.data_processor = create_stock_processor()

        # 增强的反爬虫状态管理（针对hot类型）
        self.anti_crawl_state = {
            'challenge_count': 0,
            'last_challenge_time': 0,
            'session_token': None,
            'device_fingerprint': self._generate_device_fingerprint(),
            'request_sequence': 0
        }

        try:
            self.quant = fetch_quant_raw()
        except Exception as e:
            self.logger.warning(f"Failed to fetch quant data: {e}")
            self.quant = None

        # 增强的JS上下文初始化（用于hot类型反爬虫）
        self.js_context = self._init_enhanced_js_context()

        # 更真实的浏览器headers，专门用于雪球hot请求
        self.xueqiu_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Ch-Ua': '"Google Chrome";v="120", "Chromium";v="120", "Not_A Brand";v="99"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://xueqiu.com/',
            'Origin': 'https://xueqiu.com'
        }

    def _generate_device_fingerprint(self):
        """生成设备指纹，模拟真实浏览器环境"""
        import platform
        import uuid

        # 基于系统信息生成稳定的设备指纹
        system_info = f"{platform.system()}-{platform.machine()}-{platform.processor()}"
        mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                               for elements in range(0,2*6,2)][::-1])

        fingerprint_data = {
            'screen': {'width': 1920, 'height': 1080, 'colorDepth': 24},
            'timezone': -480,  # UTC+8
            'language': 'zh-CN',
            'platform': 'MacIntel',
            'userAgent': self._get_random_user_agent(),
            'webgl': 'ANGLE (Apple, Apple M1 Pro, OpenGL 4.1)',
            'canvas': hashlib.md5(system_info.encode()).hexdigest()[:16],
            'audio': hashlib.md5(mac_address.encode()).hexdigest()[:16]
        }

        return hashlib.md5(json.dumps(fingerprint_data, sort_keys=True).encode()).hexdigest()

    def _init_enhanced_js_context(self):
        """增强的JavaScript执行环境初始化"""
        try:
            # 加载test.js - 包含MD5验证逻辑
            try:
                with open('./test.js', 'r', encoding='utf-8') as f:
                    md5_js = f.read()
            except FileNotFoundError:
                md5_js = "function getMd5(url) { return 'test_md5_' + Date.now(); }"

            # 加载acw_sc_v2.js - 包含阿里系加密参数逻辑
            try:
                with open('./acw_sc_v2.js', 'r', encoding='utf-8') as f:
                    acw_sc_v2_js = f.read()
            except FileNotFoundError:
                acw_sc_v2_js = "function get_acw_sc_v2(arg1) { return 'fallback_' + Date.now(); }"

            # 加载阿里云WAF绕过逻辑
            try:
                with open('./aliyun_waf_bypass.js', 'r', encoding='utf-8') as f:
                    waf_bypass_js = f.read()
            except FileNotFoundError:
                waf_bypass_js = """
                function bypassAliyunWaf(htmlContent) {
                    return {
                        success: false,
                        error: 'WAF bypass not available',
                        fallback: { timestamp: Date.now() }
                    };
                }
                """

            # 合并所有JS代码
            all_js = md5_js + '\n' + acw_sc_v2_js + '\n' + waf_bypass_js
            ctx = execjs.compile(all_js)
            self.logger.info("Successfully initialized enhanced JS context for hot requests")
            return ctx
        except Exception as e:
            self.logger.warning(f"Failed to load enhanced JS context: {e}")
            return None

    def _get_random_user_agent(self):
        """生成随机的User-Agent - 更新到最新版本"""
        user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15'
        ]
        return random.choice(user_agents)

    def _is_captcha_response(self, response) -> bool:
        """检查是否是验证码页面 - 增强版本支持阿里云WAF检测"""
        if response.status == 302:
            location = response.headers.get('Location', b'').decode('utf-8')
            if 'captcha' in location.lower():
                return True

        # 检查阿里云WAF特征
        if hasattr(response, 'text'):
            body_text = response.text.lower()

            # 阿里云WAF特征检测
            aliyun_waf_indicators = [
                'aliyun_waf_aa', 'aliyun_waf_oo', 'aliyun_waf_00',
                '_waf_bd8ce2ce37', 'renderdata', 'getrenderdata'
            ]

            # 传统验证码检测
            captcha_indicators = ['captcha', '验证码', '人机验证', 'robot', 'verification']

            all_indicators = aliyun_waf_indicators + captcha_indicators
            if any(indicator in body_text for indicator in all_indicators):
                triggered = [indicator for indicator in all_indicators if indicator in body_text]
                self.logger.warning(f"Anti-crawling challenge detected for hot request. Triggered: {triggered}")
                return True

        # 检查状态码
        if response.status == 403:
            self.logger.warning("403 Forbidden response detected for hot request")
            return True

        return False

    def _add_random_delay(self, min_delay=5, max_delay=15):
        """添加随机延迟模拟人类行为 - 针对hot请求"""
        # 基于反爬虫挑战次数动态调整延迟
        base_delay = random.uniform(min_delay, max_delay)
        challenge_multiplier = 1 + (self.anti_crawl_state['challenge_count'] * 0.3)
        final_delay = min(base_delay * challenge_multiplier, 45)  # 最大45秒

        self.logger.debug(f"Adding random delay for hot request: {final_delay:.2f} seconds")
        time.sleep(final_delay)

    def _create_enhanced_hot_request(self, symbol, code):
        """创建增强的热门文章请求 - 应用反爬虫技术"""
        try:
            # 更新请求序列号
            self.anti_crawl_state['request_sequence'] += 1

            # 构建基础URL
            timestamp = str(int(time.time() * 1000))
            base_url = f'https://xueqiu.com/query/v1/symbol/search/status.json?count=10&comment=0&symbol={symbol}&hl=0&source=all&sort=alpha&page=1&q=&type=11&_={timestamp}'

            # 增强URL参数
            enhanced_url = self._enhance_hot_url_with_params(base_url)

            # 准备增强的请求头
            headers = self._prepare_enhanced_hot_headers()

            # 获取雪球cookie
            cookies_xueqiu = get_xueqiu_cookie()

            # 添加反爬虫相关的cookie
            if hasattr(self, 'acw_sc_v2') and self.acw_sc_v2:
                cookies_xueqiu['acw_sc__v2'] = self.acw_sc_v2

            self.logger.info(f"Creating enhanced hot request for {symbol} (code: {code})")
            self.logger.debug(f"Enhanced hot URL: {enhanced_url[:100]}...")

            return FormRequest(
                url=enhanced_url,
                headers=headers,
                meta={
                    'cookiejar': 1,
                    'code': code,
                    'data_type': 'hot',
                    'dont_merge_cookies': False,
                    'download_timeout': 45,
                    'enhanced_request': True,
                    'symbol': symbol
                },
                method="GET",
                cookies=cookies_xueqiu,
                callback=self.parse_enhanced_hot_response,
                dont_filter=True
            )

        except Exception as e:
            self.logger.error(f"Failed to create enhanced hot request for {symbol}: {e}")
            # 降级到基础请求
            return self._create_fallback_hot_request(symbol, code)

    def _enhance_hot_url_with_params(self, url):
        """增强热门文章URL参数"""
        try:
            # 解析现有URL
            parsed = urlparse(url)
            params = parse_qs(parsed.query)

            # 添加增强参数
            enhanced_params = {
                'seq': str(self.anti_crawl_state['request_sequence']),
                'fp': self.anti_crawl_state['device_fingerprint'][:16],
                'ts': str(int(time.time() * 1000)),
                'r': str(random.random())[:8],
                'v': '6.0.0'  # API版本
            }

            # 合并参数
            for key, value in enhanced_params.items():
                params[key] = [value]

            # 如果有JS上下文，添加MD5验证
            if self.js_context:
                try:
                    # 构建临时URL用于MD5计算
                    temp_params = '&'.join([f'{k}={v[0]}' for k, v in params.items()])
                    temp_url = f'{parsed.scheme}://{parsed.netloc}{parsed.path}?{temp_params}'
                    md5 = self.js_context.call('getMd5', temp_url)
                    params['md5__1038'] = [md5]
                    self.logger.debug(f"Generated MD5 for hot request: {md5[:10]}...")
                except Exception as e:
                    self.logger.warning(f"Failed to generate MD5 for hot request: {e}")

            # 重建URL
            final_params = '&'.join([f'{k}={v[0]}' for k, v in params.items()])
            return f'{parsed.scheme}://{parsed.netloc}{parsed.path}?{final_params}'

        except Exception as e:
            self.logger.warning(f"Failed to enhance hot URL: {e}")
            return url

    def _prepare_enhanced_hot_headers(self):
        """准备增强的热门文章请求头"""
        headers = self.xueqiu_headers.copy()
        headers['User-Agent'] = self._get_random_user_agent()

        # 添加更多真实的浏览器头
        headers.update({
            'X-Device-Fingerprint': self.anti_crawl_state['device_fingerprint'][:32],
            'X-Request-Sequence': str(self.anti_crawl_state['request_sequence']),
            'X-Request-Type': 'hot-search',
            'Connection': 'keep-alive'
        })

        return headers

    def _create_fallback_hot_request(self, symbol, code):
        """创建备用的热门文章请求"""
        self.logger.info(f"Using fallback hot request for {symbol}")

        # 基础URL（与原版本相同）
        hot_url = f'https://api.xueqiu.com/query/v1/symbol/search/status.json?count=10&comment=0&symbol={symbol}&hl=0&source=all&sort=alpha&page=1&q=&type=11'
        cookies_xueqiu = get_xueqiu_cookie()

        return FormRequest(
            url=hot_url,
            meta={'cookiejar': 1, 'code': code, 'data_type': 'hot'},
            method="GET",
            cookies=cookies_xueqiu,
            callback=self.parse_response
        )

    def parse_enhanced_hot_response(self, response):
        """解析增强的热门文章响应"""
        try:
            # 检查是否遇到反爬虫挑战
            if self._is_captcha_response(response):
                self.anti_crawl_state['challenge_count'] += 1
                self.anti_crawl_state['last_challenge_time'] = time.time()
                self.logger.error(f"Anti-crawling challenge detected for hot request (count: {self.anti_crawl_state['challenge_count']})")

                # 如果挑战次数过多，增加延迟
                if self.anti_crawl_state['challenge_count'] >= 3:
                    self.logger.warning("Too many challenges for hot requests, adding extra delay")
                    self._add_random_delay(30, 60)

                # 尝试重新请求
                symbol = response.meta.get('symbol')
                code = response.meta.get('code')
                if symbol and code:
                    fallback_request = self._create_fallback_hot_request(symbol, code)
                    yield fallback_request
                return

            # 正常解析响应
            yield from self.parse_response(response)

        except Exception as e:
            self.logger.error(f"Error parsing enhanced hot response: {e}")
            # 降级到普通解析
            yield from self.parse_response(response)

    def _get_mcode(self):
        """生成东方财富API请求所需的mcode"""
        import hashlib
        import time
        
        # 获取当前时间戳，向下取整到10分钟
        timestamp = int(time.time())
        rounded_timestamp = timestamp - (timestamp % 600)
        
        # 构造加密字符串
        str_to_hash = f"eastmoney.com|{rounded_timestamp}"
        
        # 使用MD5加密
        md5 = hashlib.md5(str_to_hash.encode()).hexdigest()
        return md5

        # Redis分布式模式下不需要单独的code参数
        if self.code:
            self.logger.info(f"Running in single stock mode for code: {self.code}")
        else:
            self.logger.info("Running in distributed mode, processing from Redis queue")

    def get_spider_config(self) -> SpiderConfig:
        """获取爬虫配置 - 个股详情使用保守配置"""
        return SpiderConfig(
            concurrent_requests=1,
            download_delay=2,
            batch_size=10,
            retry_times=2
        )

    def count_size(self, key):
        """重写count_size方法，统一使用SCARD，因为我们统一使用SET类型"""
        try:
            return self.server.scard(key)
        except Exception as e:
            self.logger.error(f"Error counting queue size for key {key}: {e}")
            return 0

    def get_start_urls(self) -> list:
        """获取起始URL列表"""
        return []  # Redis分布式模式不使用此方法

    def make_requests_from_data(self, data):
        """
        从Redis队列数据创建请求
        数据格式: JSON格式 {"url": "...", "code": "...", "data_type": "..."}
        """
        # 解码数据
        if isinstance(data, bytes):
            data = data.decode('utf-8')

        try:
            # 解析JSON数据
            url_data = json.loads(data.strip())

            url = url_data.get('url')
            code = url_data.get('code')
            data_type = url_data.get('data_type')

            if not all([url, code, data_type]):
                self.logger.error(f"Invalid URL data: {url_data}")
                return None

            # 调试：记录处理的URL
            self.logger.debug(f"[分布式爬虫] 处理 {data_type} 数据，股票: {code}")
            
            # 如果是持仓数据，修改URL格式
            if data_type == 'holder':
                # 使用最新一期的数据
                report_date = time.strftime('%Y-%m-%d', time.localtime())
                url = (f'https://data.eastmoney.com/dataapi/zlsj/detail?'
                      f'SHType=&SHCode=&SCode={code}&ReportDate={report_date}&'
                      f'sortField=HOLDER_CODE&sortDirec=1&pageNum=1&pageSize=1000')

            # 根据数据类型设置不同的请求参数
            if data_type == 'hot':
                # 雪球热门文章 - 使用增强反爬虫技术
                symbol = stock_code(code)
                return self._create_enhanced_hot_request(symbol, code)
            elif data_type == 'income':
                # 雪球收入数据 - 保持原有逻辑
                cookies_xueqiu = get_xueqiu_cookie()
                headers = {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Origin': 'https://xueqiu.com',
                    'Referer': 'https://xueqiu.com/',
                }
                return FormRequest(
                    url=url,
                    headers=headers,
                    meta={
                        'cookiejar': 1,
                        'code': code,
                        'data_type': data_type,
                        'dont_merge_cookies': False
                    },
                    method="GET",
                    cookies=cookies_xueqiu,
                    callback=self.parse_response,
                    dont_filter=True
                )
            elif data_type in ['business', 'holder']:
                # 东方财富API
                headers = {
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Cookie': 'qgqp_b_id=c9411ea178550d666bbf978a5bcb7d47',
                    'DNT': '1',
                    'Host': 'emweb.securities.eastmoney.com',
                    'Pragma': 'no-cache',
                    'Referer': 'https://emweb.securities.eastmoney.com/PC_HSF10/BusinessAnalysis/Index',
                    'sec-ch-ua': '"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"macOS"',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-origin',
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/113.0.1774.57',
                    'X-Requested-With': 'XMLHttpRequest',
                    'mcode': self._get_mcode()  # 添加 mcode
                }
                return FormRequest(
                    url=url,
                    headers=headers,
                    meta={
                        'cookiejar': 1,
                        'code': code,
                        'data_type': data_type,
                        'dont_merge_cookies': False,
                        'handle_httpstatus_list': [404, 500, 502, 503, 504, 429]
                    },
                    method="GET",
                    callback=self.parse_response,
                    dont_filter=True
                )
            else:
                self.logger.error(f"Unknown data type: {data_type}")
                return None

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON data: {data}, error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error generating request for {data}: {e}")
            return None

    def start_requests(self):
        """
        单股票模式的请求生成（向后兼容）
        当直接指定code参数时使用
        """
        if not self.code:
            # Redis分布式模式，不需要生成请求
            return

        self.logger.info(f"Generating requests for single stock: {self.code}")

        cookies_xueqiu = get_xueqiu_cookie()
        symbol = stock_code(self.code)
        timestamp = str(ago_day_timestamp(0))

        # 雪球季度盈利数据
        income_url = f'https://stock.xueqiu.com/v5/stock/finance/cn/income.json?symbol={symbol}&type=all&is_detail=true&count=9&timestamp={timestamp}'
        yield FormRequest(
            url=income_url,
            meta={'cookiejar': 1, 'code': self.code, 'data_type': 'income'},
            method="GET",
            cookies=cookies_xueqiu,
            callback=self.parse_response
        )

        # 雪球个股热门文章 - 增强反爬虫版本
        hot_request = self._create_enhanced_hot_request(symbol, self.code)
        if hot_request:
            yield hot_request

        # 东方财富营收占比
        business_url = f'https://emweb.securities.eastmoney.com/PC_HSF10/BusinessAnalysis/PageAjax?code={symbol}'
        yield FormRequest(
            url=business_url,
            meta={'cookiejar': 1, 'code': self.code, 'data_type': 'business'},
            method="GET",
            callback=self.parse_response
        )

    @timing_decorator('stock_detail.parse_response')
    def parse_response(self, response):
        """统一的响应解析方法"""
        try:
            data_type = response.meta.get('data_type')

            if data_type == 'business':
                for item in self._parse_business_data(response):
                    yield item
            elif data_type == 'income':
                for item in self._parse_income_data(response):
                    yield item
            elif data_type == 'hot':
                for item in self._parse_hot_data(response):
                    if isinstance(item, MediaItem):
                        self.logger.debug(f"Processing media item: {item}")
                        yield item
                    else:
                        self.logger.warning(f"Unexpected item type for hot data: {type(item)}")
            elif data_type == 'holder':
                for item in self._parse_holder_data(response):
                    yield item
            else:
                self.logger.warning(f"Unknown data type: {data_type}")

        except Exception as e:
            self.handle_error(e, response)

    def _parse_business_data(self, response):
        """解析业务分析数据"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or not json_data.get('result') or not json_data['result'].get('data'):
                self.logger.warning(f"Invalid business response from {response.url}")
                return

            data = json_data['result']['data']
            processed_data = self._process_business_analysis(data)

            if processed_data:
                item = Stock2Item()
                item['code'] = response.meta.get('code')
                item['businessAnalysis'] = json.dumps(processed_data, ensure_ascii=False)

                processed_item = self.process_item(dict(item))
                if processed_item:
                    yield processed_item

        except Exception as e:
            self.logger.error(f"Error parsing business data: {e}")

    def _process_business_analysis(self, data: list) -> list:
        """处理业务分析数据"""
        try:
            # 按业务类型和排名过滤和排序数据
            type2_data = []  # 存储第二级分类数据
            type1_data = []  # 存储第一级分类数据
            
            for business in data:
                # 提取基本数据
                mainop_type = business.get('MAINOP_TYPE')
                rank = business.get('RANK')
                
                # 数据验证和清洗
                if not all(business.get(key) for key in ['ITEM_NAME', 'MAIN_BUSINESS_INCOME', 'MBI_RATIO']):
                    continue
                    
                # 转换收入为亿元单位
                business['MAIN_BUSINESS_INCOME'] = round(float(business['MAIN_BUSINESS_INCOME']) / 100000000, 2)
                
                # 转换比率为百分比
                if 'MBI_RATIO' in business:
                    business['MBI_RATIO'] = round(float(business['MBI_RATIO']) * 100, 2)
                
                if mainop_type == '2':  # 第二级分类
                    type2_data.append(business)
                elif mainop_type == '1':  # 第一级分类
                    type1_data.append(business)

            # 优先返回第二级分类数据，如果没有则返回第一级分类数据
            if type2_data:
                # 按收入比例降序排序
                return sorted(type2_data, key=lambda x: float(x.get('MBI_RATIO', 0)), reverse=True)
            elif type1_data:
                # 按收入比例降序排序
                return sorted(type1_data, key=lambda x: float(x.get('MBI_RATIO', 0)), reverse=True)
            
            return []

        except Exception as e:
            self.logger.error(f"Error processing business analysis: {e}")
            return []

        except Exception as e:
            self.logger.error(f"Error processing business analysis: {e}")
            return []
                
    def _parse_income_data(self, response):
        """解析收入数据"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or 'data' not in json_data:
                self.logger.warning(f"Invalid income response from {response.url}")
                return

            data = json_data.get('data', {})
            income_list = data.get('list', [])

            if not income_list:
                self.logger.info(f"No income data found for {response.meta.get('code')}")
                return

            # 处理收入数据
            processed_data = self._process_income_data_list(income_list)
            if processed_data:
                item = Stock2Item()
                item['code'] = response.meta.get('code')
                item['profit_time'] = processed_data['profit_time']
                item['profit'] = processed_data['profit']

                processed_item = self.process_item(dict(item))
                if processed_item:
                    yield processed_item

        except Exception as e:
            self.logger.error(f"Error parsing income data: {e}")

    def _process_income_data_list(self, income_list: list) -> dict:
        """处理收入数据列表"""
        try:
            profit_time = []
            profit = []

            for index in range(len(income_list)):
                if index == len(income_list) - 1:
                    continue  # 跳过最后一个元素

                current_data = income_list[index]
                next_data = income_list[index + 1]

                report_name = current_data.get('report_name', '')
                if not report_name:
                    continue

                profit_time.append(report_name)

                # 获取净利润数据
                current_profit = current_data.get('net_profit_atsopc', [0])[0] if current_data.get('net_profit_atsopc') else 0
                next_profit = next_data.get('net_profit_atsopc', [0])[0] if next_data.get('net_profit_atsopc') else 0

                # 计算季度利润（单位：亿元）
                if '一季报' in report_name:
                    quarterly_profit = round(current_profit / 100000000, 2)
                else:
                    if current_profit * next_profit > 0:
                        quarterly_profit = round((current_profit - next_profit) / 100000000, 2)
                    elif current_profit >= 0:
                        quarterly_profit = round((current_profit + abs(next_profit)) / 100000000, 2)
                    else:
                        quarterly_profit = -round((abs(current_profit) + next_profit) / 100000000, 2)

                profit.append(quarterly_profit)

            # 反转列表，使时间顺序正确
            profit_time.reverse()
            profit.reverse()

            # 格式化时间字符串
            formatted_time = ','.join(profit_time).replace(
                '三季报', '三季度'
            ).replace(
                '年报', '四季度'
            ).replace(
                '一季报', '一季度'
            ).replace(
                '中报', '二季度'
            )

            return {
                'profit_time': formatted_time,
                'profit': ','.join(str(x) for x in profit)
            }

        except Exception as e:
            self.logger.error(f"Error processing income data list: {e}")
            return {}

    def _parse_hot_data(self, response):
        """解析热门文章数据 - 增强版本"""
        try:
            # 检查响应状态
            if response.status != 200:
                self.logger.warning(f"Hot data request failed with status {response.status}: {response.url}")
                return

            # 检查是否是增强请求
            is_enhanced = response.meta.get('enhanced_request', False)
            code = response.meta.get('code', 'unknown')

            if is_enhanced:
                self.logger.info(f"Processing enhanced hot data response for {code}")

            json_data = self.parse_json_response(response)
            if not json_data:
                self.logger.warning(f"No JSON data in hot response from {response.url}")
                return

            if 'list' not in json_data:
                self.logger.warning(f"No 'list' key in hot data response from {response.url}")
                # 检查是否有错误信息
                if 'error_description' in json_data:
                    self.logger.error(f"Hot API Error: {json_data['error_description']} (Code: {json_data.get('error_code', 'Unknown')})")
                return

            report_list = json_data.get('list', [])
            if not report_list:
                self.logger.info(f"No hot reports found for {code}")
                return

            self.logger.info(f"Found {len(report_list)} hot reports for {code}")

            # 时间过滤器
            time_filter = self._get_time_filter()
            valid_count = 0

            for report in report_list:
                if self._is_valid_hot_report(report, time_filter):
                    item = self._create_hot_media_item(report)
                    if item:
                        valid_count += 1
                        yield item

            self.logger.info(f"Processed {valid_count} valid hot reports for {code}")

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error in hot data: {e}")
            self.logger.error(f"Response body preview: {response.text[:500]}")
        except Exception as e:
            self.logger.error(f"Error parsing hot data for {response.meta.get('code', 'unknown')}: {e}")
            self.logger.error(f"Response URL: {response.url}")
            self.logger.error(f"Response status: {response.status}")

    def _get_time_filter(self) -> str:
        """获取时间过滤器"""
        try:
            if self.quant and hasattr(self.quant, 'stockMediaDay'):
                return ago_day_timestr(self.quant.stockMediaDay, '%Y-%m-%d')
            else:
                return ago_day_timestr(7, '%Y-%m-%d')  # 默认7天
        except Exception as e:
            self.logger.warning(f"Failed to get time filter: {e}")
            return ago_day_timestr(7, '%Y-%m-%d')

    def _is_valid_hot_report(self, report_data: dict, time_filter: str) -> bool:
        """验证热门报告数据是否有效"""
        try:
            if not all(report_data.get(field) for field in ['id', 'title', 'created_at']):
                return False

            # 检查时间过滤
            created_at = report_data.get('created_at', 0)
            if created_at:
                create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))
                if create_time < time_filter:
                    return False

            # 检查内容质量（如果有量化配置）
            if self.quant:
                content_len = len(report_data.get('text', ''))
                title_len = len(report_data.get('title', ''))
                view_count = report_data.get('view_count', 0)

                if (hasattr(self.quant, 'stockMediaContent') and
                    content_len < self.quant.stockMediaContent):
                    return False

                if (hasattr(self.quant, 'stockMediaTitle') and
                    title_len <= self.quant.stockMediaTitle):
                    return False

                if (hasattr(self.quant, 'stockMediaView') and
                    view_count <= self.quant.stockMediaView):
                    return False

            return True
        except Exception as e:
            self.logger.warning(f"Error validating hot report: {e}")
            return False

    def _create_hot_media_item(self, report_data: dict) -> MediaItem:
        """创建热门媒体数据项"""
        try:
            # 处理时间
            created_at = report_data.get('created_at', 0)
            create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))

            # 创建数据项
            item = MediaItem()
            item['mediaId'] = str(report_data.get('id', ''))
            item['source'] = 'xueqiu'
            item['title'] = clear_html(report_data.get('title', ''))
            item['content'] = report_data.get('text', '')
            item['url'] = f"https://xueqiu.com/{report_data.get('user_id', '')}/{report_data.get('id', '')}"
            item['createTime'] = create_time
            # item['code'] = self.code  # 添加股票代码

            self.logger.debug(f"Created media item: {item}")
            return item

        except Exception as e:
            self.logger.error(f"Error creating hot media item: {e}")

    def _parse_holder_data(self, response):
        """解析股东持仓数据"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or 'data' not in json_data:
                self.logger.warning(f"Invalid holder response from {response.url}")
                return

            holder_data = json_data.get('data', [])
            code = response.meta.get('code')

            if not holder_data:
                self.logger.info(f"No holder data found for {code}")
                return

            self.logger.info(f"Found {len(holder_data)} holder records for {code}")

            # 处理每个持仓记录
            for holder in holder_data:
                item = self._create_holder_item(holder)
                if item:
                    yield item

        except Exception as e:
            self.logger.error(f"Error parsing holder data: {e}")

    def _create_holder_item(self, holder_data: dict) -> dict:
        """创建持仓数据项"""
        try:
            # 生成唯一ID
            security_code = holder_data.get('SECURITY_CODE', '')
            holder_code = holder_data.get('HOLDER_CODE', '')
            unique_id = hashlib.md5(
                (security_code + holder_code).encode("utf-8")
            ).hexdigest().lower().replace('-', '')

            # 创建数据项
            item = FundItem()
            item['id'] = unique_id
            item['code'] = security_code  # 股票代码
            item['fundCode'] = holder_code  # 持有人代码
            item['name'] = holder_data.get('HOLDER_NAME', '')  # 持有人名称
            item['type'] = holder_data.get('ORG_TYPE', '')  # 机构类型
            item['orgCode'] = holder_data.get('PARENT_ORG_CODE', '')  # 父机构代码
            item['orgName'] = holder_data.get('PARENT_ORG_NAME', '')  # 父机构名称
            item['amount'] = holder_data.get('HOLD_MARKET_CAP', 0)  # 持仓市值
            item['ratio'] = holder_data.get('TOTAL_SHARES_RATIO', 0)  # 持仓比例
            
            # 处理数据项
            processed_item = self.process_item(dict(item))

            # 添加日志记录
            self.logger.info(f"Created holder item for {security_code}: {holder_code}")
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating holder item: {e}")
            return None
