#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
雪球时间线爬虫URL测试脚本
验证修复后的URL构建是否正确
"""

import sys
import os
import time
import json
import requests

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import execjs
except ImportError as e:
    print(f"execjs not available: {e}")
    execjs = None

class XueqiuTimelineUrlTester:
    """雪球时间线URL测试器"""
    
    def __init__(self):
        self.js_context = self._init_js_context()
        
    def _init_js_context(self):
        """初始化JavaScript上下文"""
        try:
            if os.path.exists('test.js'):
                with open('test.js', 'r', encoding='utf-8') as f:
                    js_code = f.read()
                    return execjs.compile(js_code)
            else:
                print("⚠️  test.js not found")
                return None
        except Exception as e:
            print(f"⚠️  Failed to load JS context: {e}")
            return None
    
    def test_url_construction(self):
        """测试URL构建"""
        print("\n🔧 Testing URL Construction")
        
        # 测试参数
        test_users = ['4029984481', '1234567890']
        test_pages = [1, 2, 3]
        
        for user in test_users:
            print(f"\n📋 Testing user: {user}")
            
            for page in test_pages:
                try:
                    # 构建基础URL（与修复后的版本一致）
                    timestamp = str(int(time.time() * 1000))
                    base_url = f'https://xueqiu.com/v4/statuses/user_timeline.json?page={page}&user_id={user}&type=0&_={timestamp}'
                    
                    print(f"  📄 Page {page}:")
                    print(f"    Base URL: {base_url}")
                    
                    # 生成MD5验证参数
                    if self.js_context:
                        try:
                            md5 = self.js_context.call('getMd5', base_url)
                            enhanced_url = f'{base_url}&md5__1038={md5}'
                            print(f"    MD5: {md5}")
                            print(f"    Enhanced URL: {enhanced_url[:100]}...")
                        except Exception as e:
                            print(f"    ⚠️  MD5 generation failed: {e}")
                            enhanced_url = base_url
                    else:
                        enhanced_url = base_url
                        print(f"    ⚠️  No JS context, using base URL")
                    
                    # 验证URL格式
                    if self._validate_url_format(enhanced_url):
                        print(f"    ✅ URL format valid")
                    else:
                        print(f"    ❌ URL format invalid")
                        
                except Exception as e:
                    print(f"    ❌ Error: {e}")
    
    def _validate_url_format(self, url):
        """验证URL格式"""
        try:
            # 检查基本格式
            if not url.startswith('https://xueqiu.com/v4/statuses/user_timeline.json'):
                return False
            
            # 检查必要参数
            required_params = ['page=', 'user_id=', 'type=', '_=']
            for param in required_params:
                if param not in url:
                    return False
            
            return True
        except Exception:
            return False
    
    def test_original_vs_enhanced(self):
        """对比原始版本和增强版本的URL"""
        print("\n🔧 Testing Original vs Enhanced URL")
        
        user_id = '4029984481'
        page = 1
        
        # 原始版本URL（硬编码时间戳）
        original_url = f'https://xueqiu.com/v4/statuses/user_timeline.json?page={page}&user_id={user_id}&type=0&_=1743498481376'
        print(f"Original URL: {original_url}")
        
        # 增强版本URL（动态时间戳）
        timestamp = str(int(time.time() * 1000))
        enhanced_base_url = f'https://xueqiu.com/v4/statuses/user_timeline.json?page={page}&user_id={user_id}&type=0&_={timestamp}'
        print(f"Enhanced Base URL: {enhanced_base_url}")
        
        # 添加MD5参数
        if self.js_context:
            try:
                original_md5 = self.js_context.call('getMd5', original_url)
                enhanced_md5 = self.js_context.call('getMd5', enhanced_base_url)
                
                original_with_md5 = f'{original_url}&md5__1038={original_md5}'
                enhanced_with_md5 = f'{enhanced_base_url}&md5__1038={enhanced_md5}'
                
                print(f"Original with MD5: {original_with_md5[:100]}...")
                print(f"Enhanced with MD5: {enhanced_with_md5[:100]}...")
                
                print(f"Original MD5: {original_md5}")
                print(f"Enhanced MD5: {enhanced_md5}")
                
            except Exception as e:
                print(f"⚠️  MD5 generation failed: {e}")
    
    def test_request_simulation(self):
        """模拟请求测试（不实际发送）"""
        print("\n🔧 Testing Request Simulation")
        
        user_id = '4029984481'
        page = 1
        timestamp = str(int(time.time() * 1000))
        base_url = f'https://xueqiu.com/v4/statuses/user_timeline.json?page={page}&user_id={user_id}&type=0&_={timestamp}'
        
        if self.js_context:
            try:
                md5 = self.js_context.call('getMd5', base_url)
                final_url = f'{base_url}&md5__1038={md5}'
                
                print(f"Final URL: {final_url}")
                print(f"URL Length: {len(final_url)}")
                print(f"MD5 Parameter: {md5}")
                
                # 模拟请求头
                headers = {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br, zstd',
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://xueqiu.com/',
                    'Origin': 'https://xueqiu.com'
                }
                
                print(f"Request Headers: {len(headers)} headers prepared")
                print(f"✅ Request simulation successful")
                
                return True
                
            except Exception as e:
                print(f"❌ Request simulation failed: {e}")
                return False
        else:
            print(f"⚠️  No JS context available for simulation")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting Xueqiu Timeline URL Tests")
        print("=" * 60)
        
        tests = [
            self.test_url_construction,
            self.test_original_vs_enhanced,
            self.test_request_simulation
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                print(f"❌ Test failed: {e}")
        
        print("\n" + "=" * 60)
        print("📊 URL Testing Complete")
        
        if self.js_context:
            print("✅ JavaScript context available - MD5 generation working")
        else:
            print("⚠️  JavaScript context not available - URLs will work without MD5")
        
        print("\n📝 Next Steps:")
        print("   1. Ensure test.js is available for MD5 generation")
        print("   2. Configure user IDs in database (xueqiuUser field)")
        print("   3. Run: scrapy crawl xueqiu-timeline")
        print("   4. Monitor logs for successful data retrieval")

def main():
    """主函数"""
    print("Xueqiu Timeline URL Tester")
    print("Testing URL construction and MD5 generation...")
    
    tester = XueqiuTimelineUrlTester()
    tester.run_all_tests()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
